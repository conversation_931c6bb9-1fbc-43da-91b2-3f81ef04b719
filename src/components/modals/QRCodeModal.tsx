import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { BottomSheet } from "@/components/ui/bottom-sheet";
import useScreenSize from "@/hooks/useScreenSize";
import CopyButton from "../CopyButton";
import QRCode from "qrcode";

interface QRCodeModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  previewUrl: string;
}

export const QRCodeModal: React.FC<QRCodeModalProps> = ({
  isOpen,
  onOpenChange,
  previewUrl,
}) => {
  const { isMobile } = useScreenSize();
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);

  // Convert https:// URL to exp:// URL for Expo
  const expUrl = previewUrl.replace(/^https:\/\//, "exp://");

  useEffect(() => {
    if (isOpen && expUrl) {
      setIsGenerating(true);
      QRCode.toDataURL(expUrl, {
        width: 256,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      })
        .then((dataUrl) => {
          setQrCodeDataUrl(dataUrl);
        })
        .catch((error) => {
          console.error("Error generating QR code:", error);
        })
        .finally(() => {
          setIsGenerating(false);
        });
    }
  }, [isOpen, expUrl]);

  // Shared content component
  const renderContent = ({showHeader = false}: { showHeader?: boolean } = {}) => (
    <div className="relative space-y-4">
      {showHeader && <div className="flex flex-col space-y-2">
        <DialogTitle className="text-base">Preview on Device</DialogTitle>
          <DialogDescription className="text-sm">
            Scan this QR code with your device to preview the app
        </DialogDescription>
      </div>}
      
      {/* QR Code Display */}
      <div className="flex flex-col items-center space-y-4">
        <div className="bg-white p-4 rounded-lg">
          {isGenerating ? (
            <div className="w-64 h-64 flex items-center justify-center bg-gray-100 rounded">
              <div className="text-gray-500">Generating QR code...</div>
            </div>
          ) : qrCodeDataUrl ? (
            <img 
              src={qrCodeDataUrl} 
              alt="QR Code for app preview" 
              className="w-64 h-64"
            />
          ) : (
            <div className="w-64 h-64 flex items-center justify-center bg-gray-100 rounded">
              <div className="text-gray-500">Failed to generate QR code</div>
            </div>
          )}
        </div>
        
        <div className="text-center space-y-2">
          <p className="text-sm text-[#737780]">
            Open your camera app and point it at the QR code
          </p>
          <p className="text-xs text-[#737780]">
            Make sure you have Expo Go installed on your device
          </p>
        </div>
      </div>

      {/* URL Display */}
      <div className="w-full space-y-2">
        <p className="text-sm font-medium text-[#737780]">Expo URL</p>
        <div className="flex items-center w-full gap-2">
          <input
            type="text"
            aria-label="Expo URL"
            className="flex-1 w-full px-3 py-2 bg-[#1A1A1B] border border-[#242424] rounded-md text-[#DDDDE6] text-sm"
            value={expUrl}
            readOnly
          />
          <CopyButton
              showIcon={true}
              iconOnly={true}
              className="border-none bg-none"
              buttonClassName={buttonVariants({ variant: "outline", size: "sm" , className: "whitespace-nowrap rounded-lg  transition-all ease-in-out duration-200 bg-white/10 border-white/20 border hover:bg-white/20 hover:text-white text-white/40"})}
              value={expUrl}
              tooltipText="Copy"
              copiedTooltipText="Copied"
              feedbackType="tooltip"
              iconProps={{ size: 16 }}
              onCopy={() => { }}
            />
        </div>
      </div>
    </div>
  );

  // Shared footer component
  const renderFooter = () => (
    <div className="flex-row justify-center w-full flex gap-4">
      <Button className="flex-1" variant="secondary" onClick={() => onOpenChange(false)}>
        <div className="flex w-full justify-center items-center">
        Close
        </div>
      </Button>
    </div>
  );

  // For mobile, use BottomSheet
  if (isMobile) {
    return (
      <BottomSheet
        trigger={<div />} // Empty trigger since we control it via props
        title=""
        description=""
        open={isOpen}
        onOpenChange={onOpenChange}
        maxWidth="max-w-full"
        showDefaultFooter={false}
        footer={renderFooter()}
      >
        <div className="h-[60dvh] flex flex-col px-3 pt-0">
          {renderContent({ showHeader: true })}
        </div>
      </BottomSheet>
    );
  }

  // For desktop, use Dialog
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="m-4 mx-auto max-w-[calc(100vw-32px)] sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Preview on Device</DialogTitle>
          <DialogDescription>
            Scan this QR code with your device to preview the app
          </DialogDescription>
        </DialogHeader>
        <div className="p-6">
          {renderContent()}
          </div>
          
        <DialogFooter className="flex-row justify-center w-full">
          <DialogClose asChild>
            <Button variant="secondary">Close</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
