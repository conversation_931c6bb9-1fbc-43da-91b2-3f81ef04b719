import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { BottomSheet } from "@/components/ui/bottom-sheet";
import useScreenSize from "@/hooks/useScreenSize";
import CopyButton from "../CopyButton";
import QRCode from "qrcode";

interface QRCodeModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  previewUrl: string;
}

export const QRCodeModal: React.FC<QRCodeModalProps> = ({
  isOpen,
  onOpenChange,
  previewUrl,
}) => {
  const { isMobile } = useScreenSize();
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);

  // Convert https:// URL to exp:// URL for Expo
  const expUrl = previewUrl.replace(/^https:\/\//, "exp://");

  useEffect(() => {
    if (isOpen && expUrl) {
      setIsGenerating(true);
      QRCode.toDataURL(expUrl, {
        width: 256,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      })
        .then((dataUrl) => {
          setQrCodeDataUrl(dataUrl);
        })
        .catch((error) => {
          console.error("Error generating QR code:", error);
        })
        .finally(() => {
          setIsGenerating(false);
        });
    }
  }, [isOpen, expUrl]);

  // Shared content component
  const renderContent = ({showHeader = false}: { showHeader?: boolean } = {}) => (
    <div className="relative space-y-6">
      {showHeader && <div className="flex flex-col space-y-2">
        <DialogTitle className="text-xl font-medium text-white">Preview on your phone</DialogTitle>
          <DialogDescription className="text-sm text-[#9CA3AF]">
            Download Expo Go app to see your app in your phone or just directly scan the preview QR to see it in your native browser
        </DialogDescription>
      </div>}

      {/* Download Expo Go Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-white flex items-center gap-2">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2L2 7v10c0 5.55 3.84 9.95 9 11 5.16-1.05 9-5.45 9-11V7l-10-5z"/>
            </svg>
            Download Expo Go App
          </h3>
          <svg className="w-5 h-5 text-[#9CA3AF]" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>

        <p className="text-sm text-[#9CA3AF]">
          Scan this QR code to install the Expo Go app from the Play Store.
        </p>

        <div className="flex items-center justify-between bg-[#1A1A1B] rounded-lg p-4">
          <div className="flex items-center space-x-4">
            {/* QR Code for Expo Go download */}
            <div className="bg-white p-2 rounded-lg">
              <div className="w-16 h-16 bg-black flex items-center justify-center text-white text-xs">
                QR Code
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-[#2A2A2B] rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">Expo</span>
              </div>
              <div>
                <p className="text-white font-medium">Download Expo Go</p>
                <div className="flex items-center space-x-1 text-[#9CA3AF] text-sm">
                  <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  <span>External link</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Preview QR Code Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-white flex items-center gap-2">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <rect x="7" y="7" width="3" height="3"/>
              <rect x="14" y="7" width="3" height="3"/>
              <rect x="7" y="14" width="3" height="3"/>
              <path d="m14 14 3 3"/>
              <path d="m17 14-3 3"/>
            </svg>
            Preview QR Code
          </h3>
          <svg className="w-5 h-5 text-[#9CA3AF]" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </div>

        <div className="bg-[#1A1A1B] rounded-lg p-6 flex flex-col items-center space-y-4">
          {/* Main QR Code Display */}
          <div className="bg-white p-4 rounded-lg">
            {isGenerating ? (
              <div className="w-48 h-48 flex items-center justify-center bg-gray-100 rounded">
                <div className="text-gray-500 text-sm">Generating QR code...</div>
              </div>
            ) : qrCodeDataUrl ? (
              <img
                src={qrCodeDataUrl}
                alt="QR Code for app preview"
                className="w-48 h-48"
              />
            ) : (
              <div className="w-48 h-48 flex items-center justify-center bg-gray-100 rounded">
                <div className="text-gray-500 text-sm">Failed to generate QR code</div>
              </div>
            )}
          </div>

          <div className="text-center space-y-2">
            <p className="text-sm text-[#9CA3AF]">
              Scan this QR code with your camera to preview the app
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  // Shared footer component
  const renderFooter = () => (
    <div className="flex-row justify-end w-full flex gap-4">
      <Button
        className="px-6 py-2 bg-transparent border border-[#404040] text-white hover:bg-[#2A2A2B]"
        variant="outline"
        onClick={() => onOpenChange(false)}
      >
        Close
      </Button>
    </div>
  );

  // For mobile, use BottomSheet
  if (isMobile) {
    return (
      <BottomSheet
        trigger={<div />} // Empty trigger since we control it via props
        title=""
        description=""
        open={isOpen}
        onOpenChange={onOpenChange}
        maxWidth="max-w-full"
        showDefaultFooter={false}
        footer={renderFooter()}
      >
        <div className="h-[70dvh] flex flex-col px-4 pt-4 bg-[#111112]">
          {renderContent({ showHeader: true })}
        </div>
      </BottomSheet>
    );
  }

  // For desktop, use Dialog
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="m-4 mx-auto max-w-[calc(100vw-32px)] sm:max-w-lg bg-[#111112] border-[#242424] text-white">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl font-medium text-white">Preview on your phone</DialogTitle>
          <DialogDescription className="text-sm text-[#9CA3AF]">
            Download Expo Go app to see your app in your phone or just directly scan the preview QR to see it in your native browser
          </DialogDescription>
        </DialogHeader>
        <div className="px-6 pb-4">
          {renderContent()}
        </div>

        <DialogFooter className="flex-row justify-end w-full px-6 pb-6">
          <DialogClose asChild>
            <Button
              className="px-6 py-2 bg-transparent border border-[#404040] text-white hover:bg-[#2A2A2B]"
              variant="outline"
            >
              Close
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
